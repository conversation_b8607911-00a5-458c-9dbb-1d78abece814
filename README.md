# 商用空调监控调试软件

## 项目概括
本项目是一个基于 WPF 和 .NET 8 的单机版商用空调监控调试软件，专为空调机组的实时监控、数据采集、故障诊断和历史数据分析而设计。采用 MVVM 架构模式，使用最新的 Syncfusion 29.2.9 控件库提供现代化的 Windows 11 风格用户界面和丰富的数据可视化功能。软件采用传统的 license 文件授权方式，无需用户登录，为空调维护工程师和技术人员提供专业的单机调试工具。

## 软件特性
- **单机版设计**: 无需网络连接，支持离线运行
- **License 授权**: 采用传统 license 文件激活方式，无用户登录功能
- **现代化界面**: Windows 11 风格的深色/浅色主题切换
- **实时监控**: 支持 200-500ms 高频数据采集和显示
- **多项目架构**: 清晰的分层架构设计，便于维护和扩展
- **自定义协议**: 完整的 485 协议解析和通信功能

## 技术选型
- **开发框架**: WPF (Windows Presentation Foundation)
- **.NET 版本**: .NET 8.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI框架**: Syncfusion WPF Controls 29.2.9 (Windows11Dark/Light 主题)
- **图表控件**: OxyPlot.WPF 2.2.0 (实时曲线和历史数据图表)
- **串口通讯**: System.IO.Ports 9.0.5 + 自定义485协议解析
- **数据访问**: Entity Framework Core 9.0.5
- **数据存储**: SQLite (动态创建数据库)
- **依赖注入**: Microsoft.Extensions.DependencyInjection 9.0.5
- **消息传递**: CommunityToolkit.Mvvm 8.4.0
- **日志记录**: Serilog 4.2.0
- **配置管理**: Microsoft.Extensions.Configuration 9.0.5
- **版本控制**: Git
- **授权管理**: 自定义 License 文件验证
- **其他工具**:
  - AutoMapper 14.0.0 (对象映射)
  - FluentValidation 12.0.0 (数据验证)
  - Polly 8.5.2 (重试策略)
  - NUnit 4.2.2 (单元测试)

## 多项目解决方案结构

### AirMonitor.Core (公共类库)
**职责**: 主程序和注册机项目的公共类库，提供共享的数据模型、接口和工具类
**依赖**: 无外部依赖
**被依赖**: AirMonitor.WPF, AirMonitor.LicenseGenerator, AirMonitor.Infrastructure, AirMonitor.Services

- `/Models/`: 共享数据模型
  - `AirConditionerUnit.cs`: 空调机组模型
  - `SensorData.cs`: 传感器数据模型
  - `AlarmRecord.cs`: 报警记录模型
  - `ProtocolFrame.cs`: 协议帧模型
  - `LicenseInfo.cs`: 授权信息模型（主程序和注册机共享）
  - `HardwareFingerprintInfo.cs`: 硬件指纹信息模型
  - `ValidationResult.cs`: 验证结果模型
- `/Interfaces/`: 共享服务接口定义
  - `ISerialCommunicationService.cs`: 串口通信服务接口
  - `IProtocolParserService.cs`: 协议解析服务接口
  - `IDataCollectionService.cs`: 数据采集服务接口
  - `ILicenseService.cs`: 授权服务接口（主程序和注册机共享）
  - `ICryptoService.cs`: 加密服务接口（主程序和注册机共享）
  - `IHardwareFingerprintService.cs`: 硬件指纹服务接口（主程序和注册机共享）
  - `IDatabaseService.cs`: 数据库服务接口
- `/Enums/`: 共享枚举定义
  - `UnitStatus.cs`: 机组状态枚举
  - `SensorType.cs`: 传感器类型枚举
  - `AlarmLevel.cs`: 报警级别枚举
  - `LicenseType.cs`: 许可证类型枚举（主程序和注册机共享）
  - `LicenseErrorCode.cs`: 许可证错误代码枚举（主程序和注册机共享）
- `/Constants/`: 共享常量定义
  - `LicenseConstants.cs`: 许可证相关常量
  - `CryptoConstants.cs`: 加密相关常量
  - `ApplicationConstants.cs`: 应用程序常量
- `/Utilities/`: 共享工具类
  - `JsonHelper.cs`: JSON序列化工具
  - `FileHelper.cs`: 文件操作工具
  - `StringExtensions.cs`: 字符串扩展方法

### AirMonitor.Infrastructure (基础设施层)
**职责**: 提供技术基础设施和外部系统集成
**依赖**: AirMonitor.Core
- `/Data/`: 数据访问层
  - `AirMonitorDbContext.cs`: EF Core数据库上下文
  - `/Repositories/`: 仓储模式实现
  - `/Configurations/`: 实体配置
- `/Communication/`: 通信基础设施
  - `SerialPortHandler.cs`: 串口处理器
  - `ProtocolParser.cs`: 协议解析器
  - `CrcCalculator.cs`: CRC校验计算
- `/Configuration/`: 配置管理
  - `ProtocolConfigLoader.cs`: 协议配置加载器
  - `DatabaseConfigManager.cs`: 数据库配置管理器
- `/Extensions/`: 扩展方法
  - `ByteArrayExtensions.cs`: 字节数组扩展方法

### AirMonitor.Services (服务层)
**职责**: 实现业务逻辑和应用服务
**依赖**: AirMonitor.Core, AirMonitor.Infrastructure
- `/Communication/`: 通信服务
  - `SerialCommunicationService.cs`: 串口通信服务实现
  - `ProtocolParserService.cs`: 协议解析服务实现
- `/Data/`: 数据服务
  - `DataCollectionService.cs`: 数据采集服务
  - `DatabaseService.cs`: 数据库管理服务
- `/License/`: 授权服务
  - `LicenseService.cs`: 授权验证服务
  - `LicenseValidator.cs`: 授权验证器
- `/Alarm/`: 报警服务
  - `AlarmService.cs`: 报警处理服务

### AirMonitor.WPF (表示层)
**职责**: 用户界面和用户交互
**依赖**: AirMonitor.Services, AirMonitor.Core
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口界面
  - `ConnectionView.xaml`: 串口连接配置界面
  - `OutdoorUnitMonitorView.xaml`: 外机监控界面（轻量级网格布局）
  - `IndoorUnitMonitorView.xaml`: 内机监控界面（标准表格布局）
  - `HistoryDataView.xaml`: 历史数据查询界面
  - `CurveAnalysisView.xaml`: 曲线分析界面
  - `AlarmManagementView.xaml`: 故障报警管理界面
  - `SettingsView.xaml`: 系统设置界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `ConnectionViewModel.cs`: 连接配置视图模型
  - `OutdoorUnitMonitorViewModel.cs`: 外机监控视图模型
  - `IndoorUnitMonitorViewModel.cs`: 内机监控视图模型
  - `HistoryDataViewModel.cs`: 历史数据视图模型
  - `CurveAnalysisViewModel.cs`: 曲线分析视图模型
  - `AlarmManagementViewModel.cs`: 报警管理视图模型
  - `SettingsViewModel.cs`: 设置视图模型
- `/Controls/`: 自定义用户控件
  - `RealTimeCurveControl.xaml`: 实时曲线控件
  - `AlarmIndicatorControl.xaml`: 报警指示器控件
  - `UnitStatusControl.xaml`: 机组状态控件
- `/Converters/`: 值转换器
  - `BoolToVisibilityConverter.cs`: 布尔值到可见性转换器
  - `StatusToColorConverter.cs`: 状态到颜色转换器
  - `TemperatureUnitConverter.cs`: 温度单位转换器
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
  - `/Templates/`: 模板文件
  - `/Images/`: 图片资源

### AirMonitor.LicenseGenerator (License注册机)
**职责**: License文件生成、验证和管理工具
**依赖**: AirMonitor.Core
**UI风格**: 与主程序保持完全一致的界面风格和用户体验

- `/Views/`: WPF界面（统一UI风格）
  - `MainWindow.xaml`: 主界面（使用Syncfusion Windows11主题）
  - `LicenseGeneratorView.xaml`: 许可证生成界面
  - `LicenseValidatorView.xaml`: 许可证验证界面
  - `TemplateManagerView.xaml`: 模板管理界面
- `/ViewModels/`: 视图模型
  - `MainViewModel.cs`: 主界面视图模型
  - `LicenseGeneratorViewModel.cs`: 生成器视图模型
  - `LicenseValidatorViewModel.cs`: 验证器视图模型
  - `TemplateManagerViewModel.cs`: 模板管理视图模型
- `/Services/`: 注册机服务（实现Core中的接口）
  - `LicenseGeneratorService.cs`: 许可证生成服务
  - `CryptoService.cs`: 加密解密服务（实现ICryptoService）
  - `HardwareFingerprintService.cs`: 硬件指纹服务（实现IHardwareFingerprintService）
- `/Resources/`: 资源文件（与主程序共享样式）
  - `/Styles/`: 共享样式文件
  - `/Images/`: 图标和图片资源
- `/Converters/`: 值转换器（复用Core中的工具类）

### AirMonitor.Tests (测试层)
**职责**: 单元测试和集成测试
**依赖**: 所有其他项目
- `/Unit/`: 单元测试
- `/Integration/`: 集成测试
- `/Fixtures/`: 测试固件和数据

## 核心功能模块 / 界面详解
- `串口连接模块`: 提供RS485串口配置功能，包括端口选择、波特率设置、数据位配置、校验位设置等，支持自动扫描可用端口和连接状态监控。
- `通用协议解析器`: 实现可配置的485协议解析功能，支持两种解析格式（固定字节解析、参数索引解析），通过JSON配置文件定义解析规则，支持位域解析、多种数据类型转换。
- `动态数据库模块`: 根据连接的空调机组总线自动创建对应的SQLite数据库，实现数据的分类存储和管理，支持数据库的备份和恢复。
- `外机监控模块`: 提供外机的实时状态监控，采用自定义轻量级网格布局（无边框），支持任意台外机的运行数据显示，包括温度、压力、流量等参数的实时显示。
- `内机监控模块`: 提供内机的实时状态监控，采用标准表格布局，支持任意台内机的数据显示，支持任意数量参数和设备的动态配置。
- `实时曲线模块`: 使用Syncfusion图表控件实现实时数据曲线显示，支持多参数同时显示、曲线缩放、数据点标注等功能。
- `历史数据模块`: 提供历史数据的查询、筛选和导出功能，支持按时间范围、机组编号、参数类型等条件进行数据检索。
- `历史曲线模块`: 实现历史数据的曲线分析功能，支持多时间段对比、趋势分析、数据统计等高级分析功能。
- `数据回放模块`: 提供历史数据的回放功能，可以按照时间顺序重现历史运行状态，支持回放速度控制和断点续播。
- `故障报警模块`: 实现智能故障检测和报警功能，支持报警规则配置、报警历史记录、报警通知等功能。
- `License授权模块`: 实现基于license文件的本地授权验证，支持软件激活、功能模块控制，适用于公司内部使用场景。
- `系统设置模块`: 提供应用程序配置管理，包括主题切换、报警阈值设置、数据采集频率配置等。

## 数据模型设计
- **AirConditionerUnit**: { Id (int, PK), UnitCode (string), Name (string), Location (string), ModelType (string), InstallDate (DateTime), Status (enum), CreatedAt (DateTime), UpdatedAt (DateTime) }
- **SensorData**: { Id (long, PK), UnitId (int, FK), SensorType (enum), Value (decimal), Unit (string), Timestamp (DateTime), Quality (enum) }
- **AlarmRecord**: { Id (long, PK), UnitId (int, FK), AlarmType (enum), AlarmLevel (enum), Message (string), OccurredAt (DateTime), AcknowledgedAt (DateTime?), ResolvedAt (DateTime?) }
- **ProtocolFrame**: { Id (long, PK), Direction (enum), RawData (byte[]), ParsedData (string), Timestamp (DateTime), IsValid (bool) }
- **DatabaseConfig**: { Id (int, PK), UnitId (int, FK), DatabasePath (string), CreatedAt (DateTime), LastBackupAt (DateTime?) }

## WPF 架构设计

- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式，采用Syncfusion控件库提供现代化的UI体验
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定，实现命令绑定和属性通知
- **Model层**: 定义业务数据模型和业务逻辑，包括空调机组、传感器数据、报警记录等实体
- **Service层**: 提供数据访问、业务服务等功能，包括串口通讯、协议解析、数据库操作等核心服务
- **依赖注入**: 使用Microsoft DI容器管理对象生命周期和依赖关系，提高代码的可测试性和可维护性

## 界面设计规范

- **主题风格**: 采用Syncfusion Windows11Dark/Light主题，提供现代化的界面风格
- **色彩方案**:
  - 主色调: #0078D4 (Microsoft Blue)
  - 辅助色: #F3F2F1 (Light Gray)
  - 强调色: #D13438 (Alert Red), #107C10 (Success Green)
  - 背景色: 根据主题自动切换深色/浅色模式
- **字体规范**:
  - 标题: 微软雅黑, 16-20px, SemiBold
  - 正文: 微软雅黑, 14px, Regular
  - 按钮: 微软雅黑, 14px, Medium
  - 数据显示: 微软雅黑, 12px, Regular
- **控件样式**:
  - 按钮: 圆角4px，悬停效果，点击反馈
  - 文本框: 边框1px，聚焦高亮，验证状态指示
  - 数据网格: 斑马纹行，排序指示器，选中高亮
  - 图表: 平滑曲线，数据点标注，缩放控制
- **布局原则**:
  - 采用Grid和DockPanel进行主要布局
  - 使用StackPanel进行局部元素排列
  - 响应式设计，支持窗口大小调整
  - 合理的边距和间距设置

## 技术实现细节

### 485协议通讯模块
- **功能文档**: [WPF功能文档_485协议通讯模块.md](docs/WPF功能文档_485协议通讯模块.md)
- **协议规范**: 支持两种协议格式（参数读写协议、数据传输协议）
- **参数值解析**: 2字节参数值支持多种格式（单一参数、位域组合、字节组合、混合格式、枚举值）
- **通信架构**: 主从轮询模式，主设备(外机1) ←→ 从设备(子外机/内机) ←→ 监控软件
- **校验机制**: 采用Modbus CRC16校验确保数据传输可靠性

### 通用协议解析器
- **功能文档**: [WPF功能文档_通用协议解析器.md](docs/WPF功能文档_通用协议解析器.md)
- **配置文件**: [protocol_config.json](docs/protocol_config.json)
- **支持功能码**: A1、A5、12、01、02、05
- **解析格式**:
  - 格式1（固定字节解析）：A1、01功能码
  - 格式2（参数索引解析）：A5、12、02、05功能码
- **配置化设计**: 通过JSON配置文件定义解析规则，支持位域解析、多种数据类型、单位转换
- **可扩展性**: 新增功能码只需修改配置文件，无需修改代码

### License授权模块
- **功能文档**: [WPF功能文档_License注册机.md](docs/WPF功能文档_License注册机.md)
- **授权方式**: 基于本地license文件的离线授权验证，适用于公司内部使用
- **加密算法**: 采用RSA+AES混合加密确保license文件安全性
- **功能控制**: 支持按功能模块进行授权限制，灵活控制软件功能
- **内部授权**: 面向公司内部使用，无试用版概念，直接提供完整功能授权
- **硬件绑定**: 基于CPU序列号和主板序列号进行硬件绑定
- **设备监控**: 授权与监控设备数量无关联，总线上有多少台设备就监控多少台
- **授权信息**: 包含软件版本、授权功能、有效期、部门信息等
- **验证流程**: 启动时自动验证，运行时定期检查，确保授权有效性

### License注册机模块
- **功能文档**: [WPF功能文档_License注册机.md](docs/WPF功能文档_License注册机.md)
- **项目架构**: 基于AirMonitor.Core公共类库，与主程序保持一致的UI风格
- **许可证类型**: 支持4种类型（普通版、售后版、研发版、管理版）
- **UI框架**: 使用Syncfusion Windows11主题，Microsoft YaHei字体
- **核心功能**:
  - 许可证生成器：基本信息配置、类型选择、有效期设置、硬件绑定、功能授权
  - 许可证验证器：文件验证、数字签名检查、硬件指纹验证、有效期检查
  - 模板管理器：模板创建、编辑、删除、导入导出
- **安全机制**: RSA-2048数字签名、AES-256数据加密、SHA-256哈希验证
- **批量功能**: 支持Excel模板批量生成许可证文件
- **开发状态**: 基础架构已完成，核心功能已实现，需完善文件对话框和Excel功能

[其他模块的技术实现细节将在后续开发中逐步填充]

## 开发状态跟踪

### 开发优先级说明
按照从基础到高级的开发顺序，确保依赖关系正确：
1. **前置工具**: License注册机 → 许可证生成和验证
2. **基础设施层**: Core模型定义 → Infrastructure数据访问 → Services业务逻辑
3. **核心功能层**: 串口通信 → 协议解析 → 数据采集
4. **应用功能层**: 监控界面 → 数据分析 → 高级功能

| 开发阶段 | 功能模块/界面 | 项目层级 | View状态 | ViewModel状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|---------|--------------|----------|----------|---------------|--------|--------------|--------------|-----------|
| **第零阶段：前置工具** | | | | | | | | |
| 0.1 | License注册机 | LicenseGenerator | 未开始 | 未开始 | AI | 2024-12-09 | |  |
| **第一阶段：基础设施** | | | | | | | | |
| 1.1 | Core模型定义 | Core | N/A | N/A | AI | 2024-12-10 | | 业务实体和接口定义 |
| 1.2 | Infrastructure基础设施 | Infrastructure | N/A | N/A | AI | 2024-12-11 | | 数据访问和通信基础 |
| 1.3 | License授权模块 | Services | 未开始 | 未开始 | AI | 2024-12-12 | | [功能文档](docs/WPF功能文档_License授权模块.md) |
| **第二阶段：核心通信** | | | | | | | | |
| 2.1 | 串口连接模块 | Services/WPF | 未开始 | 未开始 | AI | 2024-12-14 | | [功能文档](docs/WPF功能文档_485协议通讯模块.md) |
| 2.2 | 通用协议解析器 | Services | N/A | N/A | AI | 2024-12-16 | | [功能文档](docs/WPF功能文档_通用协议解析器.md) |
| 2.3 | 动态数据库模块 | Services | N/A | N/A | AI | 2024-12-18 | | SQLite动态创建和管理 |
| **第三阶段：数据采集** | | | | | | | | |
| 3.1 | 数据采集服务 | Services | N/A | N/A | AI | 2024-12-20 | | 实时数据采集和缓存 |
| 3.2 | 主窗口框架 | WPF | 未开始 | 未开始 | AI | 2024-12-22 | | 主界面布局和导航 |
| **第四阶段：监控界面** | | | | | | | | |
| 4.1 | 外机监控模块 | WPF | 未开始 | 未开始 | AI | 2024-12-25 | | 轻量级网格布局 |
| 4.2 | 内机监控模块 | WPF | 未开始 | 未开始 | AI | 2024-12-27 | | 标准表格布局 |
| 4.3 | 实时曲线模块 | WPF | 未开始 | 未开始 | AI | 2024-12-29 | | Syncfusion图表控件 |
| **第五阶段：数据分析** | | | | | | | | |
| 5.1 | 历史数据模块 | WPF | 未开始 | 未开始 | AI | 2025-01-02 | | 数据查询和筛选 |
| 5.2 | 历史曲线模块 | WPF | 未开始 | 未开始 | AI | 2025-01-04 | | 历史数据可视化 |
| 5.3 | 数据回放模块 | WPF | 未开始 | 未开始 | AI | 2025-01-06 | | 时间轴数据回放 |
| **第六阶段：高级功能** | | | | | | | | |
| 6.1 | 故障报警模块 | Services/WPF | 未开始 | 未开始 | AI | 2025-01-08 | | 智能报警和通知 |
| 6.2 | 系统设置模块 | WPF | 未开始 | 未开始 | AI | 2025-01-10 | | 配置管理界面 |

## 代码检查与问题记录

[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化等。]

## 环境设置与运行指南

### 开发环境要求
- **操作系统**: Windows 10/11 (x64)
- **开发工具**: Visual Studio 2022 (17.8+) 或 Visual Studio Code
- **.NET SDK**: .NET 8.0 SDK
- **数据库**: SQLite (无需额外安装)
- **硬件要求**:
  - 内存: 8GB+ 推荐
  - 硬盘: 500MB+ 可用空间
  - 串口: RS485转USB适配器

### NuGet包依赖

```xml
<!-- Syncfusion WPF Controls -->
<PackageReference Include="Syncfusion.SfDataGrid.WPF" Version="29.2.9" />
<PackageReference Include="Syncfusion.SfGauge.WPF" Version="29.2.9" />
<PackageReference Include="Syncfusion.SfScheduler.WPF" Version="29.2.9" />
<PackageReference Include="Syncfusion.Themes.Windows11Dark.WPF" Version="29.2.9" />
<PackageReference Include="Syncfusion.Themes.Windows11Light.WPF" Version="29.2.9" />

<!-- 图表控件 -->
<PackageReference Include="OxyPlot.WPF" Version="2.2.0" />

<!-- Database & ORM -->
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />

<!-- Dependency Injection & Configuration -->
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />

<!-- MVVM & UI -->
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />

<!-- Logging -->
<PackageReference Include="Serilog" Version="4.2.0" />
<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />

<!-- Serial Communication -->
<PackageReference Include="System.IO.Ports" Version="9.0.5" />

<!-- Utilities -->
<PackageReference Include="AutoMapper" Version="14.0.0" />
<PackageReference Include="FluentValidation" Version="12.0.0" />
<PackageReference Include="Polly" Version="8.5.2" />
<PackageReference Include="System.Text.Json" Version="9.0.5" />

<!-- Testing -->
<PackageReference Include="NUnit" Version="4.2.2" />
<PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
<PackageReference Include="Moq" Version="4.20.72" />
<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
```

### 运行步骤
1. 克隆项目到本地
2. 使用Visual Studio打开解决方案文件
3. 还原NuGet包依赖
4. 编译并运行项目
5. 配置串口连接参数
6. 连接空调机组开始监控

### 调试命令
- **编译**: `dotnet build`
- **运行**: `dotnet run`
- **测试**: `dotnet test`
- **发布**: `dotnet publish -c Release`

## WPF性能优化

### 已实施的优化策略
- **UI虚拟化**: 对大数据量的DataGrid和ListBox启用虚拟化
- **数据绑定优化**: 使用OneWay绑定减少不必要的更新
- **图表性能**: 实时曲线采用数据点限制和滚动显示
- **内存管理**: 及时释放串口资源和数据库连接
- **异步操作**: 数据库操作和文件I/O使用异步方法

### 待优化项目
- [ ] 实现数据采集的批量处理
- [ ] 优化大量历史数据的查询性能
- [ ] 实现图表的硬件加速渲染
- [ ] 添加内存使用监控和自动清理

## 部署指南

### 发布配置
- **目标框架**: net8.0-windows
- **发布模式**: Self-contained
- **单文件发布**: 支持
- **修剪未使用代码**: 启用
- **压缩**: 启用

### 安装包制作
- 使用WiX Toolset创建MSI安装包
- 包含.NET 8运行时
- 自动注册串口驱动
- 创建桌面快捷方式和开始菜单项

### 部署检查清单
- [ ] 验证目标机器的Windows版本兼容性
- [ ] 确认串口驱动程序已正确安装
- [ ] 测试数据库文件的读写权限
- [ ] 验证Syncfusion许可证配置
- [ ] 进行完整的功能测试